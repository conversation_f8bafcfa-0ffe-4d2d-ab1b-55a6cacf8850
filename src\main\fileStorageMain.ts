import { ipcMain, dialog } from 'electron';
import fs from 'fs-extra';
import path from 'path';
import { nanoid } from 'nanoid';
import Store from 'electron-store';

// Define interfaces based on global.d.ts for clarity in main process
interface FrontendStoredFile {
  id: string;
  name: string;
  content?: string;
  type: 'document' | 'folder' | 'html' | 'markdown' | 'json' | 'placeholder';
  createdAt: string;
  updatedAt: string;
  parentId?: string | null;
  documentSettings?: DocumentSettings;
}

interface DocumentSettings {
  selectedSystemPromptId?: string | null;
  systemPromptContentOverride?: string;
  isSystemPromptPrepended?: boolean;
  selectedRestyleConfigId?: string | null;
  visibleSystemPromptIds?: string[];
  hyperlinkSavingEnabled?: boolean;
  localSystemPrompts?: any[]; // Added to match the new default structure
}

interface ProjectSettings {
  systemPrompts: SystemPrompt[];
  restyleConfigs: RestyleConfig[];
}

interface SystemPrompt {
  id: string;
  name: string;
  content: string;
}

interface RestyleConfig {
  id: string;
  name: string;
  apiUrl: string;
  apiKey: string;
  isVisible?: boolean; // Toggle for showing in document dropdown
}

// Block operation types
interface BlockSaveOperation {
  blockId: string;
  content: any; // BlockNote Block type
  operation: 'create' | 'update' | 'delete';
}

interface BlockLoadResult {
  blockId: string;
  content: any | null; // BlockNote Block type or null
  success: boolean;
  error?: string;
}

interface BlockBatchSaveResult {
  documentId: string;
  savedBlocks: string[];
  failedBlocks: { blockId: string; error: string }[];
  success: boolean;
}

interface DocumentBlocksMetadata {
  version: string;
  documentId: string;
  blockOrder: string[]; // Array of block IDs in order
  lastModified: string;
  totalBlocks: number;
}

// Saved Paragraphs Feature Interfaces
interface SavedParagraphCategory {
  id: string;
  name: string;
  color?: string; // Optional color for category visualization
  createdAt: string;
  updatedAt: string;
}

interface SavedParagraphMetadata {
  id: string;
  title: string;
  categoryId: string;
  description?: string; // Optional short description
  tags?: string[]; // Optional tags for better organization
  createdAt: string;
  updatedAt: string;
  contentPreview: string; // First ~100 characters for quick preview
  wordCount: number;
  characterCount: number;
}

const store = new Store();
const STORAGE_PATH_KEY = 'workspaceStoragePath';

const PROJECT_INDEX_FILE = 'project_index_do_not_edit.json';
const SETTINGS_API_KEYS_FILE = 'settings_api_keys.json';
const DOCUMENT_CONTENT_FILE = 'document_content.json';
const DOCUMENT_METADATA_FILE = 'document_metadata.json';
const BLOCKS_FOLDER = 'blocks';
const BLOCKS_METADATA_FILE = 'blocks_metadata.json';
const IMAGES_FOLDER = 'images';

// Saved Paragraphs constants
const SAVED_PARAGRAPHS_FOLDER = 'saved_paragraphs';
const SAVED_PARAGRAPHS_INDEX_FILE = 'paragraphs_index.json';
const SAVED_PARAGRAPHS_CATEGORIES_FILE = 'categories.json';

async function getCurrentStoragePath(): Promise<string | null> {
  return store.get(STORAGE_PATH_KEY, null) as string | null;
}

async function setCurrentStoragePath(newPath: string | null): Promise<void> {
  if (newPath) {
    store.set(STORAGE_PATH_KEY, newPath);
  } else {
    store.delete(STORAGE_PATH_KEY);
  }
}

export function initializeFileStorageHandlers() {
  ipcMain.handle('fileStorage:chooseStorageDirectory', async () => {
    const result = await dialog.showOpenDialog({ properties: ['openDirectory'] });
    if (!result.canceled && result.filePaths.length > 0) {
      const selectedPath = result.filePaths[0];
      await setCurrentStoragePath(selectedPath);

      const projectIndexPath = path.join(selectedPath, PROJECT_INDEX_FILE);
      const settingsPath = path.join(selectedPath, SETTINGS_API_KEYS_FILE);

      try {
        if (!await fs.pathExists(projectIndexPath)) {
          await fs.outputJson(projectIndexPath, [], { spaces: 2 });
        }
        if (!await fs.pathExists(settingsPath)) {
          await fs.outputJson(settingsPath, { systemPrompts: [], restyleConfigs: [] }, { spaces: 2 });
        }
      } catch (err) {
        console.error('Error initializing workspace files:', err);
        await setCurrentStoragePath(null); // Rollback storage path if initialization fails
        throw new Error('Failed to initialize workspace files.');
      }
      return selectedPath;
    }
    // If user cancels, return the current storage path instead of null
    // This preserves the existing workspace selection
    return await getCurrentStoragePath();
  });

  ipcMain.handle('fileStorage:getStoragePath', async () => {
    return getCurrentStoragePath();
  });

  ipcMain.handle('fileStorage:clearStoragePath', async () => {
    await setCurrentStoragePath(null);
    return true;
  });

  ipcMain.handle('fileStorage:getFiles', async (): Promise<FrontendStoredFile[]> => {
    const currentPath = await getCurrentStoragePath();
    if (!currentPath) return [];

    const projectIndexPath = path.join(currentPath, PROJECT_INDEX_FILE);
    let projectIndex: FrontendStoredFile[] = [];
    let updatedIndex = false;

    try {
      if (await fs.pathExists(projectIndexPath)) {
        projectIndex = await fs.readJson(projectIndexPath);
      } else {
        // If index file doesn't exist, create it and return empty
        await fs.outputJson(projectIndexPath, [], { spaces: 2 });
        return [];
      }

      // Sync with disk: Check if document folders listed in the index still exist
      const validIndexEntries: FrontendStoredFile[] = [];
      // We only check entries that are supposed to have a physical folder (documents)
      // Folders of type 'folder' are purely logical and don't have their own ID-named directory.

      const diskItemChecks: Promise<boolean>[] = projectIndex.map(async (entry) => {
        if (entry.type === 'document') {
          const docFolder = path.join(currentPath, entry.id);
          return await fs.pathExists(docFolder);
        }
        return true; // For non-document types (like 'folder'), assume they are valid as they don't have a dedicated dir
      });

      const results = await Promise.all(diskItemChecks);

      projectIndex.forEach((entry, i) => {
        if (results[i]) {
          validIndexEntries.push(entry);
        } else {
          console.warn(`Stale index entry found and will be removed: ID ${entry.id}, Name: ${entry.name}. Document folder not found.`);
          updatedIndex = true;
        }
      });

      if (updatedIndex) {
        await fs.outputJson(projectIndexPath, validIndexEntries, { spaces: 2 });
        return validIndexEntries;
      }

      // Return the original project index if no changes, or the filtered one
      return projectIndex.map(doc => ({
        id: doc.id,
        name: doc.name,
        type: doc.type,
        createdAt: doc.createdAt,
        updatedAt: doc.updatedAt,
        parentId: doc.parentId,
        // Note: content and documentSettings are not typically part of the main index listing
        // They are loaded on demand by getFileById
      }));

    } catch (error) {
      console.error('Error listing files (getFiles):', error);
      if (error.code === 'ENOENT' && !updatedIndex) { // Index file itself was not found and we didn't update it yet
         try {
            await fs.outputJson(projectIndexPath, [], { spaces: 2 }); // Attempt to create a new empty index
         } catch (initError) {
            console.error('Failed to create new index file during getFiles error recovery:', initError);
         }
         return [];
      }
      // For other errors, or if we already tried to update, return empty or rethrow
      return [];
    }
  });

  ipcMain.handle('fileStorage:createDocument', async (event, documentName: string, parentId: string | null = null): Promise<FrontendStoredFile> => {
    const currentPath = await getCurrentStoragePath();
    if (!currentPath) throw new Error('Workspace path not set.');

    const docId = nanoid();
    const docFolder = path.join(currentPath, docId);

    try {
      await fs.ensureDir(docFolder);
      await fs.ensureDir(path.join(docFolder, IMAGES_FOLDER));

      // Create an empty array string as the default content for new documents
      await fs.writeFile(path.join(docFolder, DOCUMENT_CONTENT_FILE), JSON.stringify([]), 'utf8');

      const defaultMetadata: DocumentSettings = {
        selectedSystemPromptId: null,
        systemPromptContentOverride: "",
        isSystemPromptPrepended: false,
        selectedRestyleConfigId: null,
        visibleSystemPromptIds: [],
        hyperlinkSavingEnabled: false,
        localSystemPrompts: [] // Added to match DocumentSettings interface
      };
      await fs.outputJson(path.join(docFolder, DOCUMENT_METADATA_FILE), defaultMetadata, { spaces: 2 });

      const projectIndexPath = path.join(currentPath, PROJECT_INDEX_FILE);
      let projectIndex: FrontendStoredFile[] = [];
      if (await fs.pathExists(projectIndexPath)) {
        projectIndex = await fs.readJson(projectIndexPath);
      }

      const now = new Date().toISOString();
      const newDocumentEntry: FrontendStoredFile = {
        id: docId,
        name: documentName,
        type: 'document',
        createdAt: now,
        updatedAt: now,
        parentId: parentId === undefined ? null : parentId,
      };
      projectIndex.push(newDocumentEntry);
      await fs.outputJson(projectIndexPath, projectIndex, { spaces: 2 });

      return newDocumentEntry;
    } catch (error) {
      console.error('Error creating document in workspace:', error);
      throw error;
    }
  });

  // Placeholder for future methods (ensure they are uniquely named in IPC):
  // ipcMain.handle('fileStorage:saveFile', async (event, name, dataToSave, type) => { /* ... */ });
  ipcMain.handle('fileStorage:getFileById', async (event, docId: string): Promise<FrontendStoredFile | null> => {
    const currentPath = await getCurrentStoragePath();
    if (!currentPath) throw new Error('Workspace path not set.');

    try {
      // Find the document in the project index to get its name and other details
      const projectIndexPath = path.join(currentPath, PROJECT_INDEX_FILE);
      if (!await fs.pathExists(projectIndexPath)) {
        console.error('Project index file not found.');
        return null;
      }
      const projectIndex: FrontendStoredFile[] = await fs.readJson(projectIndexPath);
      const docIndexEntry = projectIndex.find(doc => doc.id === docId);

      if (!docIndexEntry) {
        console.error(`Document with id ${docId} not found in project index.`);
        return null;
      }

      const docFolder = path.join(currentPath, docId);
      const contentPath = path.join(docFolder, DOCUMENT_CONTENT_FILE);
      const metadataPath = path.join(docFolder, DOCUMENT_METADATA_FILE);
      const blocksFolder = path.join(docFolder, BLOCKS_FOLDER);

      // Check if document folder exists
      if (!await fs.pathExists(docFolder)) {
        console.error(`Document folder for id ${docId} not found at expected location: ${docFolder}.`);
        return null;
      }

      // Check if this is a block-based document
      const isBlockBased = await fs.pathExists(blocksFolder);

      // For traditional documents, content file is required
      if (!isBlockBased && !await fs.pathExists(contentPath)) {
        console.error(`Traditional document content file for id ${docId} not found. Document may be corrupted.`);
        return null;
      }

      let content: any[]; // Changed from PartialBlock[] to any[]

      if (isBlockBased) {
        // Load block-based content
        try {
          const blocksMetadata = await loadDocumentBlocksMetadata(docFolder);
          if (blocksMetadata && blocksMetadata.blockOrder) {
            content = [];
            for (const blockId of blocksMetadata.blockOrder) {
              const blockPath = path.join(blocksFolder, `${blockId}.json`);
              if (await fs.pathExists(blockPath)) {
                try {
                  const blockContent = await fs.readJson(blockPath);
                  content.push(blockContent);
                } catch (blockError) {
                  console.error(`Error reading block ${blockId} for document ${docId}:`, blockError);
                  // Continue with other blocks
                }
              }
            }
          } else {
            console.warn(`No blocks metadata found for block-based document ${docId}. Using empty content.`);
            content = [];
          }
        } catch (error) {
          console.error(`Error loading block-based content for document ${docId}:`, error);
          content = [];
        }
      } else {
        // Load traditional content
        try {
          const contentString = await fs.readFile(contentPath, 'utf8');
          // Ensure contentString is not empty before parsing
          if (contentString.trim() === '') {
            content = [];
          } else {
            const parsedContent = JSON.parse(contentString);
            if (!Array.isArray(parsedContent)) { // Basic validation
              console.warn(`Content for ${docId} is not an array after parsing. Defaulting to empty array. Content:`, contentString);
              content = [];
            } else {
              content = parsedContent;
            }
          }
        } catch (parseError) {
          console.error(`Error parsing traditional content for ${docId} from ${contentPath}. Content may be corrupt or not valid JSON. Error:`, parseError);
          content = []; // Default to empty array on parsing error
        }
      }

      let documentSettings: DocumentSettings;
      const defaultSettingsFull: DocumentSettings = {
        selectedSystemPromptId: null,
        systemPromptContentOverride: "",
        isSystemPromptPrepended: false,
        selectedRestyleConfigId: null,
        visibleSystemPromptIds: [],
        hyperlinkSavingEnabled: false,
        localSystemPrompts: []
      };

      if (await fs.pathExists(metadataPath)) {
        try {
          const rawSettings = await fs.readJson(metadataPath);
          // Merge with defaults to ensure all keys are present, even if metadata file is from an older version
          documentSettings = { ...defaultSettingsFull, ...rawSettings };
        } catch (metaError) {
          console.warn(`Error reading or parsing document_metadata.json for ${docId}. Using default settings. Error: ${metaError.message}`);
          documentSettings = { ...defaultSettingsFull };
        }
      } else {
        console.warn(`document_metadata.json not found for ${docId}. Using default settings. The file will be created if settings are saved.`);
        documentSettings = { ...defaultSettingsFull };
      }

      return {
        ...docIndexEntry, // id, name, type, createdAt, updatedAt from index
        content: JSON.stringify(content), // Send stringified content to renderer
        documentSettings: documentSettings, // Actual settings from document_metadata.json
      };
    } catch (error) {
      console.error(`Error getting document by id ${docId}:`, error);
      throw error; // Rethrow to be caught by the renderer
    }
  });
  // ipcMain.handle('fileStorage:deleteFile', async (event, id) => { /* ... */ });
  ipcMain.handle('fileStorage:renameFile', async (event, fileId: string, newName: string): Promise<{renamedItem: FrontendStoredFile, updatedChildren?: FrontendStoredFile[]}> => {
    const currentPath = await getCurrentStoragePath();
    if (!currentPath) {
      throw new Error('Workspace path not set. Cannot rename file.');
    }

    const projectIndexPath = path.join(currentPath, PROJECT_INDEX_FILE);
    if (!await fs.pathExists(projectIndexPath)) {
      throw new Error('Project index file not found. Cannot rename file.');
    }

    try {
      let projectIndex: FrontendStoredFile[] = await fs.readJson(projectIndexPath);
      const docIndex = projectIndex.findIndex(doc => doc.id === fileId);

      if (docIndex === -1) {
        throw new Error(`Document with id ${fileId} not found in project index. Cannot rename.`);
      }

      projectIndex[docIndex].name = newName;
      projectIndex[docIndex].updatedAt = new Date().toISOString();

      await fs.outputJson(projectIndexPath, projectIndex, { spaces: 2 });

      // The concept of updatedChildren might not apply directly here as we are renaming a document (file)
      // and not a folder that might have its children's paths affected.
      // The ID (folder name) isn't changing.
      return { renamedItem: projectIndex[docIndex], updatedChildren: [] };

    } catch (error) {
      console.error(`Error renaming document with id ${fileId}:`, error);
      throw error; // Rethrow to be caught by the renderer
    }
  });
  // ... and so on for other existing FileStorageAPI methods, adapted for the new workspace structure.

  ipcMain.handle('fileStorage:saveDocumentContent', async (event, docId: string, contentString: string): Promise<void> => {
    const currentPath = await getCurrentStoragePath();
    if (!currentPath) throw new Error('Workspace path not set.');

    const docFolder = path.join(currentPath, docId);
    const contentFilePath = path.join(docFolder, DOCUMENT_CONTENT_FILE);
    const blocksFolder = path.join(docFolder, BLOCKS_FOLDER);

    try {
      if (!await fs.pathExists(docFolder)) {
        console.warn(`Document folder for ${docId} not found during save. This might indicate an issue.`);
        throw new Error(`Document folder ${docId} does not exist.`);
      }

      // Check if this is a block-based document and warn about deprecated usage
      const isBlockBased = await fs.pathExists(blocksFolder);
      if (isBlockBased) {
        console.warn(`[DEPRECATED] saveDocumentContent called on block-based document ${docId}. Consider using saveDocumentBlocks instead for better performance and data integrity.`);
      }

      await fs.writeFile(contentFilePath, contentString, 'utf8');
      console.log(`[fileStorageMain] Content for ${docId} saved successfully to ${contentFilePath}. Length: ${contentString.length}`);

      // Update the 'updatedAt' timestamp in the project index
      const projectIndexPath = path.join(currentPath, PROJECT_INDEX_FILE);
      if (await fs.pathExists(projectIndexPath)) {
        const projectIndex: FrontendStoredFile[] = await fs.readJson(projectIndexPath);
        const docIndex = projectIndex.findIndex(doc => doc.id === docId);
        if (docIndex !== -1) {
          projectIndex[docIndex].updatedAt = new Date().toISOString();
          await fs.outputJson(projectIndexPath, projectIndex, { spaces: 2 });
        }
      }
    } catch (error) {
      console.error(`Error saving document content for ${docId}:`, error);
      throw error;
    }
  });

  ipcMain.handle('fileStorage:saveDocumentMetadata', async (event, docId: string, metadata: DocumentSettings): Promise<void> => {
    const currentPath = await getCurrentStoragePath();
    if (!currentPath) throw new Error('Workspace path not set.');

    try {
      const docFolder = path.join(currentPath, docId);
      const metadataPath = path.join(docFolder, DOCUMENT_METADATA_FILE);

      if (!await fs.pathExists(docFolder)) {
        throw new Error(`Document folder for id ${docId} not found.`);
      }

      await fs.outputJson(metadataPath, metadata, { spaces: 2 });

      // Update updatedAt timestamp in project index
      const projectIndexPath = path.join(currentPath, PROJECT_INDEX_FILE);
      let projectIndex: FrontendStoredFile[] = await fs.readJson(projectIndexPath);
      const docIndex = projectIndex.findIndex(doc => doc.id === docId);
      if (docIndex !== -1) {
        projectIndex[docIndex].updatedAt = new Date().toISOString();
        await fs.outputJson(projectIndexPath, projectIndex, { spaces: 2 });
      } else {
        console.warn(`Document with id ${docId} not found in project index during metadata save.`);
      }
    } catch (error) {
      console.error(`Error saving document metadata for id ${docId}:`, error);
      throw error;
    }
  });

  ipcMain.handle('fileStorage:deleteFile', async (event, itemIdToDelete: string): Promise<{ success: boolean; deletedIds: string[] }> => {
    const currentPath = await getCurrentStoragePath();
    if (!currentPath) {
      console.error('Workspace path not set. Cannot delete item.');
      return { success: false, deletedIds: [] };
    }

    const projectIndexPath = path.join(currentPath, PROJECT_INDEX_FILE);
    if (!await fs.pathExists(projectIndexPath)) {
      console.error('Project index file not found. Cannot delete item.');
      // If index doesn't exist, can't do much. Assume item is already "gone" from index perspective.
      return { success: true, deletedIds: [itemIdToDelete] };
    }

    try {
      let projectIndex: FrontendStoredFile[] = await fs.readJson(projectIndexPath);
      const itemToDelete = projectIndex.find(item => item.id === itemIdToDelete);

      if (!itemToDelete) {
        console.warn(`Item with id ${itemIdToDelete} not found in project index. No action taken.`);
        return { success: true, deletedIds: [itemIdToDelete] }; // Item not in index, so it's effectively deleted from index.
      }

      const idsToDeleteCollector: string[] = [];
      const foldersToDeleteFromDisk: string[] = []; // Physical document folders

      function collectDescendants(currentItemId: string) {
        idsToDeleteCollector.push(currentItemId);
        const currentItemEntry = projectIndex.find(it => it.id === currentItemId);
        if (currentItemEntry && currentItemEntry.type === 'document') {
          foldersToDeleteFromDisk.push(path.join(currentPath!, currentItemId));
        }

        const children = projectIndex.filter(item => item.parentId === currentItemId);
        for (const child of children) {
          collectDescendants(child.id);
        }
      }

      collectDescendants(itemIdToDelete);

      // Perform physical deletions for document folders
      for (const folderPath of foldersToDeleteFromDisk) {
        if (await fs.pathExists(folderPath)) {
          await fs.remove(folderPath);
          console.log(`Deleted folder from disk: ${folderPath}`);
        } else {
          console.warn(`Document folder ${folderPath} for deletion not found on disk, but removing from index.`);
        }
      }

      // Update the project index by filtering out all collected IDs
      const remainingFiles = projectIndex.filter(item => !idsToDeleteCollector.includes(item.id));
      await fs.outputJson(projectIndexPath, remainingFiles, { spaces: 2 });

      console.log(`Deleted items from index: ${idsToDeleteCollector.join(', ')}`);
      return { success: true, deletedIds: idsToDeleteCollector };

    } catch (error) {
      console.error(`Error deleting item with id ${itemIdToDelete} and its descendants:`, error);
      return { success: false, deletedIds: [] };
    }
  });

  // New handlers for project settings
  ipcMain.handle('fileStorage:loadProjectSettings', async (): Promise<ProjectSettings> => {
    const currentPath = await getCurrentStoragePath();
    if (!currentPath) throw new Error('Workspace path not set. Cannot load settings.');
    const settingsPath = path.join(currentPath, SETTINGS_API_KEYS_FILE);
    try {
      if (!await fs.pathExists(settingsPath)) {
         // If settings file doesn't exist, create it with defaults
        const defaultSettings: ProjectSettings = { systemPrompts: [], restyleConfigs: [] };
        await fs.outputJson(settingsPath, defaultSettings, { spaces: 2 });
        return defaultSettings;
      }
      return await fs.readJson(settingsPath) as ProjectSettings;
    } catch (error) {
      console.error('Error loading project settings:', error);
      // Return default settings in case of an error to prevent app crash
      return { systemPrompts: [], restyleConfigs: [] };
    }
  });

  ipcMain.handle('fileStorage:saveProjectSettings', async (event, settings: ProjectSettings): Promise<void> => {
    const currentPath = await getCurrentStoragePath();
    if (!currentPath) throw new Error('Workspace path not set. Cannot save settings.');
    const settingsPath = path.join(currentPath, SETTINGS_API_KEYS_FILE);
    try {
      await fs.outputJson(settingsPath, settings, { spaces: 2 });
    } catch (error) {
      console.error('Error saving project settings:', error);
      throw error;
    }
  });

  ipcMain.handle('fileStorage:duplicateFile', async (event, fileIdToDuplicate: string, suggestedNewName: string): Promise<FrontendStoredFile> => {
    const currentPath = await getCurrentStoragePath();
    if (!currentPath) {
      throw new Error('Workspace path not set. Cannot duplicate file.');
    }

    const projectIndexPath = path.join(currentPath, PROJECT_INDEX_FILE);
    if (!await fs.pathExists(projectIndexPath)) {
      throw new Error('Project index file not found. Cannot duplicate file.');
    }

    try {
      let projectIndex: FrontendStoredFile[] = await fs.readJson(projectIndexPath);
      const originalDocEntry = projectIndex.find(doc => doc.id === fileIdToDuplicate);

      if (!originalDocEntry) {
        throw new Error(`Document with id ${fileIdToDuplicate} not found. Cannot duplicate.`);
      }
      if (originalDocEntry.type !== 'document') {
        throw new Error('Only documents can be duplicated through this function.'); // Folders might need a different duplication logic
      }

      const originalDocFolder = path.join(currentPath, fileIdToDuplicate);
      if (!await fs.pathExists(originalDocFolder)) {
        // This case should ideally be caught by getFiles sync, but as a safeguard:
        throw new Error(`Source document folder for id ${fileIdToDuplicate} not found. Consider running a refresh.`);
      }

      // Handle display name conflict for the new document
      let finalNewName = suggestedNewName;
      let copySuffix = 1;
      const originalParentId = originalDocEntry.parentId; // Duplicate will have the same parent

      while (projectIndex.some(doc => doc.name === finalNewName && doc.parentId === originalParentId && doc.type === 'document')) {
        finalNewName = `${suggestedNewName} (${copySuffix++})`;
      }

      const newDocId = nanoid();
      const newDocFolder = path.join(currentPath, newDocId);

      await fs.copy(originalDocFolder, newDocFolder);

      const now = new Date().toISOString();
      const newDocumentEntry: FrontendStoredFile = {
        id: newDocId,
        name: finalNewName, // Use the conflict-resolved name
        type: 'document',
        createdAt: now,
        updatedAt: now,
        parentId: originalParentId, // Assign the same parentId as the original
        documentSettings: originalDocEntry.documentSettings // This was not explicitly copied before, but fs.copy handles metadata.json
                                                        // However, the index entry itself doesn't store it, this is fine.
      };

      projectIndex.push(newDocumentEntry);
      await fs.outputJson(projectIndexPath, projectIndex, { spaces: 2 });

      // Return the new entry, potentially with its content and settings if needed by frontend immediately
      // For now, just the index entry. Frontend can call getFileById if full data is needed.
      return newDocumentEntry;
    } catch (error) {
      console.error(`Error duplicating document with id ${fileIdToDuplicate}:`, error);
      throw error;
    }
  });

  ipcMain.handle('fileStorage:createFolder', async (event, folderName: string, parentId: string | null = null): Promise<FrontendStoredFile> => {
    const currentPath = await getCurrentStoragePath();
    if (!currentPath) throw new Error('Workspace path not set.');

    const projectIndexPath = path.join(currentPath, PROJECT_INDEX_FILE);
    let projectIndex: FrontendStoredFile[] = [];
    if (await fs.pathExists(projectIndexPath)) {
      projectIndex = await fs.readJson(projectIndexPath);
    }

    // Optional: Check for folder name conflict within the same parentId
    const siblingExists = projectIndex.some(
      item => item.parentId === (parentId === undefined ? null : parentId) && item.name === folderName && item.type === 'folder'
    );
    if (siblingExists) {
      throw new Error(`A folder named "${folderName}" already exists in this location.`);
    }

    const folderId = nanoid();
    const now = new Date().toISOString();

    const newFolderEntry: FrontendStoredFile = {
      id: folderId,
      name: folderName,
      type: 'folder',
      createdAt: now,
      updatedAt: now,
      parentId: parentId === undefined ? null : parentId,
    };

    projectIndex.push(newFolderEntry);
    await fs.outputJson(projectIndexPath, projectIndex, { spaces: 2 });

    return newFolderEntry;
  });

  ipcMain.handle('fileStorage:exportFile', async (event, fileToExport: { id: string } ): Promise<boolean> => {
    const currentPath = await getCurrentStoragePath();
    if (!currentPath) {
      throw new Error('Workspace path not set. Cannot export file.');
    }
    if (!fileToExport || !fileToExport.id) {
      throw new Error('Invalid file data provided for export.');
    }

    const docId = fileToExport.id;
    const docFolderToExport = path.join(currentPath, docId);

    if (!await fs.pathExists(docFolderToExport)) {
      throw new Error(`Document folder for id ${docId} not found. Cannot export.`);
    }

    // Get document name from index for suggesting a save name
    const projectIndexPath = path.join(currentPath, PROJECT_INDEX_FILE);
    let docName = docId; // Default to ID if not found in index
    if (await fs.pathExists(projectIndexPath)) {
        const projectIndex: FrontendStoredFile[] = await fs.readJson(projectIndexPath);
        const docEntry = projectIndex.find(doc => doc.id === docId);
        if (docEntry && docEntry.name) {
            docName = docEntry.name;
        }
    }

    const result = await dialog.showSaveDialog({
      title: 'Export Document',
      defaultPath: `${docName}_exported`, // Suggests a folder name
      properties: ['createDirectory'] // Though we are saving a folder, showSaveDialog often implies a file.
                                      // User needs to select/create a folder where the doc folder will be copied.
                                      // Or, better, let user select a directory and we copy *into* it.
    });

    if (result.canceled || !result.filePath) {
      return false;
    }

    // The result.filePath will be the path to where the user wants to save.
    // We should copy the docFolderToExport *into* this path, or make this the new folder name.
    // Let's assume result.filePath is the target directory for the exported document's contents.
    const destinationPath = result.filePath;
    // Ensure the destination is a directory. If the user selected a file, use its parent dir.
    // Or, more simply, let fs.copy create the target directory.

    try {
      await fs.copy(docFolderToExport, destinationPath, { overwrite: false, errorOnExist: true });
      return true;
    } catch (error) {
      console.error(`Error exporting document id ${docId} to ${destinationPath}:`, error);
      // Inform the user about the error, e.g., if the directory already exists or other issues.
      throw new Error(`Failed to export document. Error: ${error.message}`);
    }
  });

  ipcMain.handle('fileStorage:moveItem', async (event, itemId: string, newParentId: string | null): Promise<{movedItem: FrontendStoredFile, updatedChildren?: FrontendStoredFile[]}> => {
    const currentPath = await getCurrentStoragePath();
    if (!currentPath) throw new Error('Workspace path not set.');

    const projectIndexPath = path.join(currentPath, PROJECT_INDEX_FILE);
    if (!await fs.pathExists(projectIndexPath)) {
      throw new Error('Project index not found. Cannot move item.');
    }

    let projectIndex: FrontendStoredFile[] = await fs.readJson(projectIndexPath);
    const itemToMoveIndex = projectIndex.findIndex(item => item.id === itemId);

    if (itemToMoveIndex === -1) {
      throw new Error(`Item with ID ${itemId} not found.`);
    }

    const itemToMove = projectIndex[itemToMoveIndex];

    // Check 1: If newParentId is not null, ensure the target parent exists and is a folder
    if (newParentId !== null) {
      const parentItem = projectIndex.find(item => item.id === newParentId);
      if (!parentItem) {
        throw new Error(`Target parent folder with ID ${newParentId} not found.`);
      }
      if (parentItem.type !== 'folder') {
        throw new Error(`Target parent with ID ${newParentId} is not a folder.`);
      }
    }

    // Check 2: Prevent moving an item into itself
    if (itemToMove.id === newParentId) {
      throw new Error('Cannot move an item into itself.');
    }

    // Check 3: Prevent moving a folder into one of its own descendants
    if (itemToMove.type === 'folder' && newParentId !== null) {
      let currentAncestorId = newParentId;
      while (currentAncestorId !== null) {
        if (currentAncestorId === itemToMove.id) {
          throw new Error('Cannot move a folder into one of its own descendants.');
        }
        const ancestor = projectIndex.find(item => item.id === currentAncestorId);
        currentAncestorId = ancestor ? ancestor.parentId : null;
      }
    }

    // Update parentId and updatedAt timestamp
    projectIndex[itemToMoveIndex].parentId = newParentId === undefined ? null : newParentId;
    projectIndex[itemToMoveIndex].updatedAt = new Date().toISOString();

    await fs.outputJson(projectIndexPath, projectIndex, { spaces: 2 });

    // updatedChildren is not really applicable here as we are not changing paths, only logical parent
    return { movedItem: projectIndex[itemToMoveIndex], updatedChildren: [] };
  });

  // Block-based storage handlers for enhanced auto-save system
  ipcMain.handle('fileStorage:saveDocumentBlocks', async (event, docId: string, blocks: BlockSaveOperation[]): Promise<BlockBatchSaveResult> => {
    const currentPath = await getCurrentStoragePath();
    if (!currentPath) throw new Error('Workspace path not set.');

    const docFolder = path.join(currentPath, docId);
    const blocksFolder = path.join(docFolder, BLOCKS_FOLDER);

    try {
      // Ensure document and blocks folder exist
      await fs.ensureDir(blocksFolder);

      const savedBlocks: string[] = [];
      const failedBlocks: { blockId: string; error: string }[] = [];

      // Process each block operation atomically
      for (const block of blocks) {
        try {
          const blockPath = path.join(blocksFolder, `${block.blockId}.json`);

          if (block.operation === 'create' || block.operation === 'update') {
            // Atomic write: write to temp file first, then rename
            const tempPath = `${blockPath}.tmp`;
            await fs.writeFile(tempPath, JSON.stringify(block.content, null, 2), 'utf8');
            await fs.rename(tempPath, blockPath);

            savedBlocks.push(block.blockId);
          } else if (block.operation === 'delete') {
            if (await fs.pathExists(blockPath)) {
              await fs.remove(blockPath);
            }

            savedBlocks.push(block.blockId);
          }
        } catch (blockError) {
          console.error(`Error processing block ${block.blockId}:`, blockError);
          failedBlocks.push({
            blockId: block.blockId,
            error: blockError instanceof Error ? blockError.message : String(blockError)
          });
        }
      }

      // Update document metadata with block information
      await updateDocumentBlocksMetadata(docFolder, docId);

      // Update project index timestamp
      await updateProjectIndexTimestamp(currentPath, docId);

      return {
        documentId: docId,
        savedBlocks,
        failedBlocks,
        success: failedBlocks.length === 0
      };

    } catch (error) {
      console.error(`Error saving blocks for document ${docId}:`, error);
      throw error;
    }
  });

  ipcMain.handle('fileStorage:loadDocumentBlocks', async (event, docId: string, blockIds?: string[]): Promise<BlockLoadResult[]> => {
    const currentPath = await getCurrentStoragePath();
    if (!currentPath) throw new Error('Workspace path not set.');

    const docFolder = path.join(currentPath, docId);
    const blocksFolder = path.join(docFolder, BLOCKS_FOLDER);

    try {
      const results: BlockLoadResult[] = [];

      if (!await fs.pathExists(blocksFolder)) {
        // No blocks folder means this is likely a legacy document or no blocks exist
        if (blockIds) {
          // Return failed results for requested blocks
          return blockIds.map(blockId => ({
            blockId,
            content: null,
            success: false,
            error: 'Document is not in block format or blocks folder does not exist'
          }));
        }
        return [];
      }

      if (blockIds && blockIds.length > 0) {
        // Load specific blocks
        for (const blockId of blockIds) {
          try {
            const blockPath = path.join(blocksFolder, `${blockId}.json`);
            if (await fs.pathExists(blockPath)) {
              const blockContent = await fs.readJson(blockPath);
              results.push({
                blockId,
                content: blockContent,
                success: true
              });
            } else {
              results.push({
                blockId,
                content: null,
                success: false,
                error: 'Block file not found'
              });
            }
          } catch (blockError) {
            console.error(`Error loading block ${blockId}:`, blockError);
            results.push({
              blockId,
              content: null,
              success: false,
              error: blockError instanceof Error ? blockError.message : String(blockError)
            });
          }
        }
      } else {
        // Load all blocks
        const blockFiles = await fs.readdir(blocksFolder);
        const jsonFiles = blockFiles.filter(file => file.endsWith('.json'));

        for (const file of jsonFiles) {
          try {
            const blockId = file.replace('.json', '');
            const blockPath = path.join(blocksFolder, file);
            const blockContent = await fs.readJson(blockPath);
            results.push({
              blockId,
              content: blockContent,
              success: true
            });
          } catch (blockError) {
            console.error(`Error loading block file ${file}:`, blockError);
            const blockId = file.replace('.json', '');
            results.push({
              blockId,
              content: null,
              success: false,
              error: blockError instanceof Error ? blockError.message : String(blockError)
            });
          }
        }
      }

      return results;

    } catch (error) {
      console.error(`Error loading blocks for document ${docId}:`, error);
      throw error;
    }
  });

  ipcMain.handle('fileStorage:migrateDocumentToBlocks', async (event, docId: string): Promise<any> => {
    const currentPath = await getCurrentStoragePath();
    if (!currentPath) throw new Error('Workspace path not set.');

    const docFolder = path.join(currentPath, docId);
    const contentPath = path.join(docFolder, DOCUMENT_CONTENT_FILE);
    const blocksFolder = path.join(docFolder, BLOCKS_FOLDER);

    try {
      // Check if already migrated
      if (await fs.pathExists(blocksFolder)) {
        return {
          success: true,
          message: 'Document already migrated to block format',
          alreadyMigrated: true
        };
      }

      // Load legacy content
      let legacyContent: any[] = [];
      if (await fs.pathExists(contentPath)) {
        try {
          const contentString = await fs.readFile(contentPath, 'utf8');
          if (contentString.trim()) {
            const parsedContent = JSON.parse(contentString);
            if (Array.isArray(parsedContent)) {
              legacyContent = parsedContent;
            }
          }
        } catch (parseError) {
          console.warn(`Could not parse legacy content for ${docId}, starting with empty document:`, parseError);
        }
      }

      // Create blocks folder
      await fs.ensureDir(blocksFolder);

      // Convert content to individual block files
      const migratedBlocks: string[] = [];
      for (const block of legacyContent) {
        if (block && block.id) {
          try {
            const blockPath = path.join(blocksFolder, `${block.id}.json`);
            await fs.writeFile(blockPath, JSON.stringify(block, null, 2), 'utf8');
            migratedBlocks.push(block.id);
          } catch (blockError) {
            console.error(`Error migrating block ${block.id}:`, blockError);
          }
        }
      }

      // Create blocks metadata
      await updateDocumentBlocksMetadata(docFolder, docId);

      // Backup legacy content file
      if (await fs.pathExists(contentPath)) {
        const backupPath = path.join(docFolder, `${DOCUMENT_CONTENT_FILE}.legacy`);
        await fs.copy(contentPath, backupPath);
      }

      // Update project index timestamp
      await updateProjectIndexTimestamp(currentPath, docId);

      return {
        success: true,
        message: `Successfully migrated ${migratedBlocks.length} blocks`,
        migratedBlockCount: migratedBlocks.length,
        migratedBlocks,
        alreadyMigrated: false
      };

    } catch (error) {
      console.error(`Error migrating document ${docId} to blocks:`, error);
      throw error;
    }
  });

  ipcMain.handle('fileStorage:getDocumentBlocksMetadata', async (event, docId: string): Promise<any> => {
    const currentPath = await getCurrentStoragePath();
    if (!currentPath) throw new Error('Workspace path not set.');

    const docFolder = path.join(currentPath, docId);

    try {
      const metadata = await loadDocumentBlocksMetadata(docFolder);
      return metadata;
    } catch (error) {
      console.error(`Error getting blocks metadata for document ${docId}:`, error);
      throw error;
    }
  });

  // Additional block-based handlers for the enhanced auto-save system
  ipcMain.handle('fileStorage:saveDocumentBlocksMetadata', async (event, docId: string, metadata: DocumentBlocksMetadata): Promise<void> => {
    const currentPath = await getCurrentStoragePath();
    if (!currentPath) throw new Error('Workspace path not set.');

    const docFolder = path.join(currentPath, docId);
    const metadataPath = path.join(docFolder, BLOCKS_METADATA_FILE);

    try {
      await fs.ensureDir(docFolder);
      // Atomic write for metadata
      const tempPath = `${metadataPath}.tmp`;
      await fs.writeFile(tempPath, JSON.stringify(metadata, null, 2), 'utf8');
      await fs.rename(tempPath, metadataPath);

      // Update project index timestamp
      await updateProjectIndexTimestamp(currentPath, docId);
    } catch (error) {
      console.error(`Error saving blocks metadata for document ${docId}:`, error);
      throw error;
    }
  });

  ipcMain.handle('fileStorage:loadDocumentBlocksMetadata', async (event, docId: string): Promise<DocumentBlocksMetadata | null> => {
    const currentPath = await getCurrentStoragePath();
    if (!currentPath) throw new Error('Workspace path not set.');

    const docFolder = path.join(currentPath, docId);

    try {
      const metadata = await loadDocumentBlocksMetadata(docFolder);
      return metadata;
    } catch (error) {
      console.error(`Error loading blocks metadata for document ${docId}:`, error);
      throw error;
    }
  });

  ipcMain.handle('fileStorage:deleteDocumentBlocks', async (event, docId: string, blockIds: string[]): Promise<{ deletedBlocks: string[]; failedBlocks: string[] }> => {
    const currentPath = await getCurrentStoragePath();
    if (!currentPath) throw new Error('Workspace path not set.');

    const docFolder = path.join(currentPath, docId);
    const blocksFolder = path.join(docFolder, BLOCKS_FOLDER);

    try {
      const deletedBlocks: string[] = [];
      const failedBlocks: string[] = [];

      for (const blockId of blockIds) {
        try {
          const blockPath = path.join(blocksFolder, `${blockId}.json`);
          if (await fs.pathExists(blockPath)) {
            await fs.remove(blockPath);
            deletedBlocks.push(blockId);
          } else {
            // Block doesn't exist, consider it successfully "deleted"
            deletedBlocks.push(blockId);
          }
        } catch (error) {
          console.error(`Error deleting block ${blockId}:`, error);
          failedBlocks.push(blockId);
        }
      }

      // Update blocks metadata
      await updateDocumentBlocksMetadata(docFolder, docId);

      // Update project index timestamp
      await updateProjectIndexTimestamp(currentPath, docId);

      return { deletedBlocks, failedBlocks };
    } catch (error) {
      console.error(`Error deleting blocks for document ${docId}:`, error);
      throw error;
    }
  });

  ipcMain.handle('fileStorage:isDocumentBlockBased', async (event, docId: string): Promise<boolean> => {
    const currentPath = await getCurrentStoragePath();
    if (!currentPath) throw new Error('Workspace path not set.');

    const docFolder = path.join(currentPath, docId);
    const blocksFolder = path.join(docFolder, BLOCKS_FOLDER);

    try {
      return await fs.pathExists(blocksFolder);
    } catch (error) {
      console.error(`Error checking if document ${docId} is block-based:`, error);
      return false;
    }
  });

  // Saved Paragraphs IPC Handlers
  ipcMain.handle('fileStorage:getSavedParagraphs', async (): Promise<SavedParagraphMetadata[]> => {
    const currentPath = await getCurrentStoragePath();
    if (!currentPath) return [];

    const paragraphsIndexPath = path.join(currentPath, SAVED_PARAGRAPHS_FOLDER, SAVED_PARAGRAPHS_INDEX_FILE);

    try {
      if (await fs.pathExists(paragraphsIndexPath)) {
        return await fs.readJson(paragraphsIndexPath);
      }
      return [];
    } catch (error) {
      console.error('Error loading saved paragraphs:', error);
      return [];
    }
  });

  ipcMain.handle('fileStorage:getSavedParagraphById', async (event, id: string): Promise<{ metadata: SavedParagraphMetadata; content: any } | null> => {
    const currentPath = await getCurrentStoragePath();
    if (!currentPath) return null;

    const paragraphsFolder = path.join(currentPath, SAVED_PARAGRAPHS_FOLDER);
    const paragraphsIndexPath = path.join(paragraphsFolder, SAVED_PARAGRAPHS_INDEX_FILE);
    const contentPath = path.join(paragraphsFolder, `${id}.json`);

    try {
      // Load metadata from index
      if (!await fs.pathExists(paragraphsIndexPath)) {
        return null;
      }

      const paragraphs: SavedParagraphMetadata[] = await fs.readJson(paragraphsIndexPath);
      const metadata = paragraphs.find(p => p.id === id);

      if (!metadata) {
        return null;
      }

      // Load content
      if (!await fs.pathExists(contentPath)) {
        console.warn(`Content file for paragraph ${id} not found`);
        return { metadata, content: null };
      }

      const content = await fs.readJson(contentPath);
      return { metadata, content };
    } catch (error) {
      console.error(`Error loading saved paragraph ${id}:`, error);
      return null;
    }
  });

  ipcMain.handle('fileStorage:saveParagraph', async (event, metadata: Omit<SavedParagraphMetadata, 'id' | 'createdAt' | 'updatedAt'>, content: any): Promise<SavedParagraphMetadata> => {
    const currentPath = await getCurrentStoragePath();
    if (!currentPath) throw new Error('Workspace path not set.');

    const paragraphsFolder = path.join(currentPath, SAVED_PARAGRAPHS_FOLDER);
    const paragraphsIndexPath = path.join(paragraphsFolder, SAVED_PARAGRAPHS_INDEX_FILE);

    try {
      // Ensure folder exists
      await fs.ensureDir(paragraphsFolder);

      // Create new paragraph metadata
      const id = nanoid();
      const now = new Date().toISOString();
      const newParagraph: SavedParagraphMetadata = {
        ...metadata,
        id,
        createdAt: now,
        updatedAt: now
      };

      // Save content to separate file
      const contentPath = path.join(paragraphsFolder, `${id}.json`);
      await fs.outputJson(contentPath, content, { spaces: 2 });

      // Update index
      let paragraphs: SavedParagraphMetadata[] = [];
      if (await fs.pathExists(paragraphsIndexPath)) {
        paragraphs = await fs.readJson(paragraphsIndexPath);
      }

      paragraphs.push(newParagraph);
      await fs.outputJson(paragraphsIndexPath, paragraphs, { spaces: 2 });

      return newParagraph;
    } catch (error) {
      console.error('Error saving paragraph:', error);
      throw error;
    }
  });

  ipcMain.handle('fileStorage:updateParagraph', async (event, id: string, metadata: Partial<SavedParagraphMetadata>, content?: any): Promise<SavedParagraphMetadata> => {
    const currentPath = await getCurrentStoragePath();
    if (!currentPath) throw new Error('Workspace path not set.');

    const paragraphsFolder = path.join(currentPath, SAVED_PARAGRAPHS_FOLDER);
    const paragraphsIndexPath = path.join(paragraphsFolder, SAVED_PARAGRAPHS_INDEX_FILE);
    const contentPath = path.join(paragraphsFolder, `${id}.json`);

    try {
      if (!await fs.pathExists(paragraphsIndexPath)) {
        throw new Error('Paragraphs index not found');
      }

      // Load and update index
      const paragraphs: SavedParagraphMetadata[] = await fs.readJson(paragraphsIndexPath);
      const index = paragraphs.findIndex(p => p.id === id);

      if (index === -1) {
        throw new Error(`Paragraph with id ${id} not found`);
      }

      // Update metadata
      const updatedParagraph: SavedParagraphMetadata = {
        ...paragraphs[index],
        ...metadata,
        id, // Ensure ID doesn't change
        updatedAt: new Date().toISOString()
      };

      paragraphs[index] = updatedParagraph;
      await fs.outputJson(paragraphsIndexPath, paragraphs, { spaces: 2 });

      // Update content if provided
      if (content !== undefined) {
        await fs.outputJson(contentPath, content, { spaces: 2 });
      }

      return updatedParagraph;
    } catch (error) {
      console.error(`Error updating paragraph ${id}:`, error);
      throw error;
    }
  });

  ipcMain.handle('fileStorage:deleteParagraph', async (event, id: string): Promise<boolean> => {
    const currentPath = await getCurrentStoragePath();
    if (!currentPath) throw new Error('Workspace path not set.');

    const paragraphsFolder = path.join(currentPath, SAVED_PARAGRAPHS_FOLDER);
    const paragraphsIndexPath = path.join(paragraphsFolder, SAVED_PARAGRAPHS_INDEX_FILE);
    const contentPath = path.join(paragraphsFolder, `${id}.json`);

    try {
      if (!await fs.pathExists(paragraphsIndexPath)) {
        return false;
      }

      // Update index
      const paragraphs: SavedParagraphMetadata[] = await fs.readJson(paragraphsIndexPath);
      const filteredParagraphs = paragraphs.filter(p => p.id !== id);

      if (filteredParagraphs.length === paragraphs.length) {
        return false; // Paragraph not found
      }

      await fs.outputJson(paragraphsIndexPath, filteredParagraphs, { spaces: 2 });

      // Delete content file
      if (await fs.pathExists(contentPath)) {
        await fs.remove(contentPath);
      }

      return true;
    } catch (error) {
      console.error(`Error deleting paragraph ${id}:`, error);
      throw error;
    }
  });

  ipcMain.handle('fileStorage:getSavedParagraphCategories', async (): Promise<SavedParagraphCategory[]> => {
    const currentPath = await getCurrentStoragePath();
    if (!currentPath) return [];

    const categoriesPath = path.join(currentPath, SAVED_PARAGRAPHS_FOLDER, SAVED_PARAGRAPHS_CATEGORIES_FILE);

    try {
      if (await fs.pathExists(categoriesPath)) {
        return await fs.readJson(categoriesPath);
      }
      return [];
    } catch (error) {
      console.error('Error loading saved paragraph categories:', error);
      return [];
    }
  });

  ipcMain.handle('fileStorage:saveParagraphCategory', async (event, category: Omit<SavedParagraphCategory, 'id' | 'createdAt' | 'updatedAt'>): Promise<SavedParagraphCategory> => {
    const currentPath = await getCurrentStoragePath();
    if (!currentPath) throw new Error('Workspace path not set.');

    const paragraphsFolder = path.join(currentPath, SAVED_PARAGRAPHS_FOLDER);
    const categoriesPath = path.join(paragraphsFolder, SAVED_PARAGRAPHS_CATEGORIES_FILE);

    try {
      // Ensure folder exists
      await fs.ensureDir(paragraphsFolder);

      // Create new category
      const id = nanoid();
      const now = new Date().toISOString();
      const newCategory: SavedParagraphCategory = {
        ...category,
        id,
        createdAt: now,
        updatedAt: now
      };

      // Load existing categories
      let categories: SavedParagraphCategory[] = [];
      if (await fs.pathExists(categoriesPath)) {
        categories = await fs.readJson(categoriesPath);
      }

      categories.push(newCategory);
      await fs.outputJson(categoriesPath, categories, { spaces: 2 });

      return newCategory;
    } catch (error) {
      console.error('Error saving paragraph category:', error);
      throw error;
    }
  });

  ipcMain.handle('fileStorage:updateParagraphCategory', async (event, id: string, category: Partial<SavedParagraphCategory>): Promise<SavedParagraphCategory> => {
    const currentPath = await getCurrentStoragePath();
    if (!currentPath) throw new Error('Workspace path not set.');

    const categoriesPath = path.join(currentPath, SAVED_PARAGRAPHS_FOLDER, SAVED_PARAGRAPHS_CATEGORIES_FILE);

    try {
      if (!await fs.pathExists(categoriesPath)) {
        throw new Error('Categories file not found');
      }

      const categories: SavedParagraphCategory[] = await fs.readJson(categoriesPath);
      const index = categories.findIndex(c => c.id === id);

      if (index === -1) {
        throw new Error(`Category with id ${id} not found`);
      }

      // Update category
      const updatedCategory: SavedParagraphCategory = {
        ...categories[index],
        ...category,
        id, // Ensure ID doesn't change
        updatedAt: new Date().toISOString()
      };

      categories[index] = updatedCategory;
      await fs.outputJson(categoriesPath, categories, { spaces: 2 });

      return updatedCategory;
    } catch (error) {
      console.error(`Error updating paragraph category ${id}:`, error);
      throw error;
    }
  });

  ipcMain.handle('fileStorage:deleteParagraphCategory', async (event, id: string): Promise<boolean> => {
    const currentPath = await getCurrentStoragePath();
    if (!currentPath) throw new Error('Workspace path not set.');

    const categoriesPath = path.join(currentPath, SAVED_PARAGRAPHS_FOLDER, SAVED_PARAGRAPHS_CATEGORIES_FILE);

    try {
      if (!await fs.pathExists(categoriesPath)) {
        return false;
      }

      const categories: SavedParagraphCategory[] = await fs.readJson(categoriesPath);
      const filteredCategories = categories.filter(c => c.id !== id);

      if (filteredCategories.length === categories.length) {
        return false; // Category not found
      }

      await fs.outputJson(categoriesPath, filteredCategories, { spaces: 2 });
      return true;
    } catch (error) {
      console.error(`Error deleting paragraph category ${id}:`, error);
      throw error;
    }
  });
}
async function updateDocumentBlocksMetadata(docFolder: string, docId: string): Promise<void> {
  const blocksFolder = path.join(docFolder, BLOCKS_FOLDER);
  const metadataPath = path.join(docFolder, BLOCKS_METADATA_FILE);

  try {
    if (!await fs.pathExists(blocksFolder)) {
      return;
    }

    const blockFiles = await fs.readdir(blocksFolder);
    const jsonFiles = blockFiles.filter(file => file.endsWith('.json'));

    const blockIds = jsonFiles.map(file => file.replace('.json', ''));
    const now = new Date().toISOString();

    const metadata = {
      documentId: docId,
      blockCount: blockIds.length,
      blockIds: blockIds,
      lastUpdated: now,
      version: '1.0'
    };

    await fs.writeFile(metadataPath, JSON.stringify(metadata, null, 2), 'utf8');
  } catch (error) {
    console.error(`Error updating blocks metadata for ${docId}:`, error);
    throw error;
  }
}

async function loadDocumentBlocksMetadata(docFolder: string): Promise<any> {
  const metadataPath = path.join(docFolder, BLOCKS_METADATA_FILE);

  try {
    if (await fs.pathExists(metadataPath)) {
      return await fs.readJson(metadataPath);
    }
    return null;
  } catch (error) {
    console.error(`Error loading blocks metadata:`, error);
    return null;
  }
}

async function updateProjectIndexTimestamp(currentPath: string, docId: string): Promise<void> {
  const projectIndexPath = path.join(currentPath, PROJECT_INDEX_FILE);

  try {
    if (await fs.pathExists(projectIndexPath)) {
      const projectIndex: FrontendStoredFile[] = await fs.readJson(projectIndexPath);
      const docIndex = projectIndex.findIndex(doc => doc.id === docId);

      if (docIndex !== -1) {
        projectIndex[docIndex].updatedAt = new Date().toISOString();
        await fs.outputJson(projectIndexPath, projectIndex, { spaces: 2 });
      }
    }
  } catch (error) {
    console.error(`Error updating project index timestamp for ${docId}:`, error);
    // Don't throw here as this is not critical for the main operation
  }
}
